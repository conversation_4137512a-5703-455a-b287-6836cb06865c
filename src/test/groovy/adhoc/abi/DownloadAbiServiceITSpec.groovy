package adhoc.abi

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Logger
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.time.Duration
import java.util.concurrent.TimeUnit
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.test.util.ReflectionTestUtils
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import spock.lang.Shared

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class DownloadAbiServiceITSpec extends BaseAdhocITSpec {

	@Shared
	def abiParserLogger = LoggerFactory.getLogger(AbiParser.class) as Logger

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties properties

	@Autowired
	S3ClientAdaptor s3ClientAdaptor

	@DynamicPropertySource
	static void configureProperties(DynamicPropertyRegistry registry) {
		registry.add("local-stack.end-point", { "http://localhost:" + AdhocHelper.getLocalStackPort() })
		registry.add("eagerStart", { "false" })
		registry.add("aws.dynamodb.table-prefix", { "" })
	}

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account",
			"Provider"
		])
		// Start log appender to capture logs
		abiParserLogger.addAppender(logAppender)
	}

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Successful Service Startup with command line runner and process ABI files
	 * Verifies service starts successfully with all dependencies valid
	 * Expected: Service logs "Started bc monitoring" and extracted from correct JSON path based on format
	 */
	def "Should service start up successfully with command line runner and process ABI files"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }
	}

	/**
	 * Should processes ABI files from multiple zones
	 * Verifies service processes ABI files from multiple zones
	 * Expected: Service logs "getting s3 abi object" for each zone
	 */
	def "Should processes ABI files from multiple zones"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3001", [
			"Token",
			"Account",
			"Provider"
		])

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000") }
		assert messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3001") }
		assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }
	}

	/**
	 * Should correctly parses ABI files based on truffle environment variable
	 * Verifies service correctly parses ABI files based on truffle environment variable
	 * Expected: No exceptions are thrown and service starts successfully
	 */
	def "Should correctly parses ABI files based on truffle environment variable"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		clearS3Bucket()
		properties.setAbiFormat("truffle")
		AdhocHelper.uploadTruffleAbiFiles(s3Client, TEST_BUCKET, "3000", ["Account"])

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts successfully"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }
	}

	/**
	 * Should skip non-json file
	 * Verifies service skips non-json files
	 * Expected: Service logs "This object will be skipped because the extension is not .json" and no exceptions are thrown
	 */
	def "Should skip non-json file"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		clearS3Bucket()
		AdhocHelper.uploadInvalidAbiFiles(s3Client, TEST_BUCKET, "3000", ["nonJson.txt"])

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("This object will be skipped because the extension is not .json:") }
	}

	/**
	 * Should skip deeply nested files and only process direct child objects
	 * Verifies service skips deeply nested files and only process direct child objects
	 * Expected: Service logs "getting s3 abi object" for direct child objects only and no exceptions are thrown
	 */
	def "Should skip deeply nested files and only process direct child objects"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		AdhocHelper.uploadAbiFilesWithNestedPath(s3Client, TEST_BUCKET, "3000", ["FinancialCheck"])

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Account.json") }
		assert messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Token.json") }
		assert messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/Provider.json") }
		assert !messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000/FinancialCheck.json") }
		assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }
	}

	/**
	 * Should start fails when parsing malformed JSON
	 * Verifies service startup fails
	 * Expected: Service logs "Failed to parse S3 abi object:"
	 */
	def "Should start fails when parsing malformed JSON"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		AdhocHelper.uploadInvalidAbiFiles(s3Client, TEST_BUCKET, "3000", ["MalformedJson.json"])

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Service fails to start"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Failed to parse S3 abi object: 3000/MalformedJson.json") }
		assert !messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Error starting bc monitoring") }
	}

	/**
	 * Should start fails when ABI file lacks required abi section
	 * Verifies service startup fails
	 * Expected: Service logs "ABI section not found in JSON"
	 */
	def "Should start fails when ABI file lacks required abi section"() {
		given: "Valid environment with accessible dependencies"
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		AdhocHelper.uploadInvalidAbiFiles(s3Client, TEST_BUCKET, "3000", ["LacksAbiSection.json"])

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Service fails to start"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("ABI section not found in JSON") }
		assert messages.any { it.contains("Failed to parse S3 abi object: 3000/LacksAbiSection.json") }
		assert !messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Error starting bc monitoring") }
	}

	/**
	 * Should start fails when s3 connect timeout
	 * Verifies service startup fails
	 * Expected: Service logs "Error starting bc monitoring"
	 */
	def "Should start fails when s3 connect timeout"() {
		given: "S3 client with very short timeout to simulate timeout scenario"
		clearS3Bucket()

		// Create S3 client with extremely short timeout (1ms) to force timeout
		def timeoutS3Client = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
				AwsBasicCredentials.create("access123", "secret123")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.overrideConfiguration(ClientOverrideConfiguration.builder()
				.apiCallTimeout(Duration.ofMillis(1))  // Very short timeout
				.apiCallAttemptTimeout(Duration.ofMillis(1))
				.build())
				.build()

		// Replace the S3Client in the S3ClientAdaptor with our timeout client
		ReflectionTestUtils.setField(s3ClientAdaptor, "s3Client", timeoutS3Client)

		// Setup required dependencies
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Service fails to start due to S3 timeout"
		def logMessages = logAppender.list*.formattedMessage
		def logStackTraces = logAppender.list.collect {
			it.throwableProxy?.stackTraceElementProxyArray?.collect { it.toString() }?.join(" ") ?: ""
		}

		assert !logMessages.any { it.contains("Started bc monitoring") }
		assert logMessages.any { it.contains("Error starting bc monitoring") }
		assert logStackTraces.any { it.contains("ApiCallTimeoutException") }

		cleanup: "Close the timeout S3 client"
		timeoutS3Client?.close()
	}

	/**
	 * Should fails to start when S3 bucket is inaccessible
	 * Verifies service startup fails
	 * Expected: Service logs "Error starting bc monitoring"
	 */
	def "Should fails to start when S3 bucket is inaccessible"() {
		given: "InValid S3 connection"
		def invalidS3 = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:100"))
				.credentialsProvider(s3Client.serviceClientConfiguration().credentialsProvider())
				.region(s3Client.serviceClientConfiguration().region())
				.build()
		ReflectionTestUtils.setField(s3ClientAdaptor, "s3Client", invalidS3)

		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Service fails to start due to invalid S3 connection"
		def logMessages = logAppender.list*.formattedMessage
		def logStackTraces = logAppender.list.collect {
			it.throwableProxy?.stackTraceElementProxyArray?.collect { it.toString() }?.join(" ") ?: ""
		}

		assert !logMessages.any { it.contains("Started bc monitoring") }
		assert logMessages.any { it.contains("Error starting bc monitoring") }
		assert logStackTraces.any { it.contains("SdkClientException") }

		cleanup: "Close the invalid S3 client"
		invalidS3?.close()
	}
}

package adhoc.event_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Logger
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.Log
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams
import spock.lang.Shared

import java.math.BigInteger
import java.time.Instant
import java.util.Optional
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class EventMonitoringITSpec extends BaseAdhocITSpec {

    @Shared
    def abiParserLogger = LoggerFactory.getLogger(AbiParser.class) as Logger

    @Autowired
    ApplicationContext applicationContext

    @MockitoSpyBean
    Web3jConfig web3jConfig

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("local-stack.end-point", { "http://localhost:" + AdhocHelper.getLocalStackPort() })
        registry.add("eagerStart", { "false" })
        registry.add("aws.dynamodb.table-prefix", { "" })
    }

    def setupSpec() {
        setupSpecCommon()
    }

    def cleanupSpec() {
        cleanupSpecCommon()
    }

    def setup() {
        setupCommon()
        // Upload real ABI files to
        AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "Token",
                "Account",
                "Provider"
        ])
        // Start log appender to capture logs
        abiParserLogger.addAppender(logAppender)
    }

    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }

    def cleanup() {
        cleanupCommon()
    }

    /**
     * Should detects and processes events from new blockchain blocks
     * Verifies service correctly detects and processes events
     * Expected: Events extracted, parsed, and saved to DynamoDB
     */
    def "Should detects and processes events from new blockchain blocks"() {
        given: "An empty DynamoDB BlockHeight and all dependencies available"
//        def asyncExecutionComplete = new CountDownLatch(1)

        // Create mock events and blocks similar to the reference test
        def mockEvents = createMockNewHeadsNotificationsWithBlocks()

        // Setup event stream with mock events
//        setUpEventStreamWithBlocks(mockEvents, asyncExecutionComplete)
        setUpEventStream(mockEvents)
        setUpPendingEvent(Collections.emptyList())

        when: "The service starts"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 15, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Wait for async execution to complete"
        println("Waiting for async execution to complete...")
//        def completed = asyncExecutionComplete.await(30, TimeUnit.SECONDS)
//        println("Async execution completed: ${completed}")

        and: "Service starts and processes ABI files successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("Monitoring events...") }

        and: "Events are processed and saved to DynamoDB"
        // Check if events were saved to DynamoDB
        def eventsInDb = scanEventsTable()
        println("Events found in DynamoDB: ${eventsInDb?.size() ?: 'null'}")
        if (eventsInDb != null) {
            eventsInDb.each { event ->
                println("Event: ${event}")
            }
        }
    }

    /**
     * Helper method to create mock NewHeadsNotification events with blocks
     * Based on the reference test from EthEventLogDaoSpec.groovy
     */
    private List<NewHeadsNotification> createMockNewHeadsNotificationsWithBlocks() {
        def notifications = []

        // Create first mock notification for block 1000
        def notification1 = Mock(NewHeadsNotification)
        def params1 = Mock(NotificationParams)
        def result1 = Mock(NewHeadsNotification.Result)
        result1.getNumber() >> "0x3e8" // 1000 in hex
        params1.getResult() >> result1
        notification1.getParams() >> params1
        notifications.add(notification1)

        // Create second mock notification for block 1001
        def notification2 = Mock(NewHeadsNotification)
        def params2 = Mock(org.web3j.protocol.websocket.events.NotificationParams)
        def result2 = Mock(org.web3j.protocol.websocket.events.NewHeadsNotification.Result)
        result2.getNumber() >> "0x3e9" // 1001 in hex
        params2.getResult() >> result2
        notification2.getParams() >> params2
        notifications.add(notification2)

        // Create third mock notification for block 1002
        def notification3 = Mock(NewHeadsNotification)
        def params3 = Mock(org.web3j.protocol.websocket.events.NotificationParams)
        def result3 = Mock(org.web3j.protocol.websocket.events.NewHeadsNotification.Result)
        result3.getNumber() >> "0x3ea" // 1002 in hex
        params3.getResult() >> result3
        notification3.getParams() >> params3
        notifications.add(notification3)

        return notifications
    }

    /**
     * Setup event stream with blocks that contain mock events
     * Based on the reference test from EthEventLogDaoSpec.groovy
     */
    protected void setUpEventStreamWithBlocks(List<NewHeadsNotification> notifications, CountDownLatch asyncExecutionComplete) {
        // Create a flowable that triggers the subscription
        def testFlowable = Flowable.create({ emitter ->
            notifications.each { notification ->
                emitter.onNext(notification)
                Thread.sleep(2000) // Small delay between notifications
            }
            // Wait for async completion - give more time for processing
            Thread.start {
                Thread.sleep(100) // Wait 5 seconds for processing to complete
                asyncExecutionComplete.countDown()
            }
            emitter.onComplete()
        }, BackpressureStrategy.BUFFER)

        // Setup the mock to return our flowable
        web3j.newHeadsNotifications() >> testFlowable

        // Setup ethGetBlockByNumber mock to return blocks with mock events
        web3j.ethGetBlockByNumber(_, true) >> {
            def mockRequest = Mock(Request)
            def mockEthBlock = Mock(EthBlock)
            def mockBlock = Mock(EthBlock.Block)

            // Create mock block with realistic data
            mockBlock.getNumber() >> BigInteger.valueOf(1000)
            mockBlock.getTimestamp() >> BigInteger.valueOf(Instant.now().getEpochSecond() - 10)

            // Create mock transactions for the block
            def mockTransaction = Mock(EthBlock.TransactionObject)
            mockTransaction.getHash() >> "0xabc123def456"
            mockTransaction.get() >> "0xabc123def456"

            mockBlock.getTransactions() >> [mockTransaction]
            mockEthBlock.getBlock() >> mockBlock

            // Create a real CompletableFuture that executes the full chain
            def future = CompletableFuture.supplyAsync({
                Thread.sleep(50) // Simulate async delay
                return mockEthBlock
            })

            mockRequest.sendAsync() >> future
            return mockRequest
        }

        // Setup ethGetTransactionReceipt mock to return receipts with logs
        web3j.ethGetTransactionReceipt(_) >> {
            def mockReceiptRequest = Mock(Request)
            def mockReceiptResponse = Mock(org.web3j.protocol.core.methods.response.EthGetTransactionReceipt)
            def mockReceipt = Mock(org.web3j.protocol.core.methods.response.TransactionReceipt)

            // Create mock logs/events for the receipt
            def mockLog = Mock(Log)
            mockLog.getAddress() >> "******************************************"
            mockLog.getTransactionHash() >> "0xabc123def456"
            mockLog.getLogIndex() >> BigInteger.valueOf(0)
            mockLog.getBlockNumber() >> BigInteger.valueOf(1000)
            mockLog.getData() >> "0x0000000000000000000000000000000000000000000000000000000000000001"
            mockLog.getTopics() >> ["0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"]

            mockReceipt.getLogs() >> [mockLog]
            mockReceiptResponse.getTransactionReceipt() >> Optional.of(mockReceipt)

            def receiptFuture = CompletableFuture.supplyAsync({
                Thread.sleep(30) // Simulate async delay
                return mockReceiptResponse
            })

            mockReceiptRequest.sendAsync() >> receiptFuture
            return mockReceiptRequest
        }
    }

    /**
     * Helper method to scan events table and return all events
     */
    private List<Map<String, Object>> scanEventsTable() {
        try {
            def scanRequest = software.amazon.awssdk.services.dynamodb.model.ScanRequest.builder()
                    .tableName(EVENTS_TABLE)
                    .build()

            def scanResponse = dynamoDbClient.scan(scanRequest)
            return scanResponse.items().collect { item ->
                def event = [:]
                item.each { key, value ->
                    if (value.s() != null) {
                        event[key] = value.s()
                    } else if (value.n() != null) {
                        event[key] = value.n()
                    }
                }
                return event
            }
        } catch (Exception e) {
            println("Error scanning events table: ${e.message}")
            return []
        }
    }
}
